#!/usr/bin/env python3
"""
ESP32 Ethernet OTA 测试脚本
用于测试OTA更新功能
"""

import requests
import sys
import time
import os

def test_ota_status(ip, port=8080):
    """测试OTA状态接口"""
    try:
        url = f"http://{ip}:{port}/status"
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 设备状态获取成功:")
            print(f"   OTA进行中: {status.get('otaInProgress', 'Unknown')}")
            print(f"   进度: {status.get('progress', 'Unknown')}%")
            print(f"   剩余空间: {status.get('freeSpace', 'Unknown')} bytes")
            print(f"   固件版本: {status.get('version', 'Unknown')}")
            return True
        else:
            print(f"❌ 状态请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_ota_upload(ip, firmware_path, port=8080):
    """测试OTA固件上传"""
    if not os.path.exists(firmware_path):
        print(f"❌ 固件文件不存在: {firmware_path}")
        return False
    
    try:
        url = f"http://{ip}:{port}/update"
        
        print(f"📤 开始上传固件: {firmware_path}")
        print(f"   文件大小: {os.path.getsize(firmware_path)} bytes")
        
        with open(firmware_path, 'rb') as f:
            files = {'firmware': ('firmware.bin', f, 'application/octet-stream')}
            
            # 发送POST请求
            response = requests.post(url, files=files, timeout=60)
            
            if response.status_code == 200:
                print("✅ 固件上传成功!")
                print("   设备将重启并应用新固件")
                return True
            else:
                print(f"❌ 上传失败: HTTP {response.status_code}")
                print(f"   响应: {response.text[:200]}")
                return False
                
    except Exception as e:
        print(f"❌ 上传失败: {e}")
        return False

def test_web_interface(ip, port=8080):
    """测试Web界面"""
    try:
        url = f"http://{ip}:{port}/"
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print("✅ Web界面访问成功")
            if "ESP32 OTA" in response.text:
                print("   界面内容正确")
                return True
            else:
                print("   ⚠️  界面内容可能不正确")
                return False
        else:
            print(f"❌ Web界面访问失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Web界面访问失败: {e}")
        return False

def main():
    if len(sys.argv) < 2:
        print("使用方法:")
        print(f"  {sys.argv[0]} <ESP32_IP> [firmware.bin]")
        print("")
        print("示例:")
        print(f"  {sys.argv[0]} *************")
        print(f"  {sys.argv[0]} ************* .pio/build/seeed_xiao_esp32s3/firmware.bin")
        sys.exit(1)
    
    ip = sys.argv[1]
    firmware_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    print(f"🔍 测试ESP32 OTA功能 - IP: {ip}")
    print("=" * 50)
    
    # 测试Web界面
    print("\n1. 测试Web界面...")
    web_ok = test_web_interface(ip)
    
    # 测试状态接口
    print("\n2. 测试状态接口...")
    status_ok = test_ota_status(ip)
    
    # 测试固件上传（如果提供了固件文件）
    if firmware_path:
        print("\n3. 测试固件上传...")
        
        # 再次检查状态，确保没有正在进行的OTA
        if test_ota_status(ip):
            upload_ok = test_ota_upload(ip, firmware_path)
            
            if upload_ok:
                print("\n⏳ 等待设备重启...")
                time.sleep(5)
                
                print("🔄 检查设备是否重启成功...")
                for i in range(10):
                    time.sleep(2)
                    if test_ota_status(ip):
                        print("✅ 设备重启成功，OTA更新完成!")
                        break
                    print(f"   等待中... ({i+1}/10)")
                else:
                    print("⚠️  设备可能还在重启中，请稍后手动检查")
    
    print("\n" + "=" * 50)
    print("测试完成!")
    
    # 显示浏览器访问链接
    print(f"\n🌐 浏览器访问: http://{ip}:8080")

if __name__ == "__main__":
    main()
