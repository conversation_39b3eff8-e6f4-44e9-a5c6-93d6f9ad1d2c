/*
 *  w5500_config.h
 *
 *  Copyright 2024
 *
 *  Configuration header for W5500 socket implementation
 *  for lib60870 Platform Abstraction Layer
 */

#ifndef W5500_CONFIG_H_
#define W5500_CONFIG_H_

#ifdef __cplusplus
extern "C" {
#endif

// W5500 specific configuration

// Enable debug output for W5500 socket operations
#ifndef DEBUG_SOCKET
#define DEBUG_SOCKET 0
#endif

// Maximum number of sockets supported by W5500
#ifndef MAX_SOCK_NUM
#define MAX_SOCK_NUM 8
#endif

// Default SPI settings for W5500
#ifndef W5500_SPI_SPEED
#define W5500_SPI_SPEED 14000000  // 14 MHz
#endif

// Default CS pin for W5500
#ifndef W5500_CS_PIN
#define W5500_CS_PIN 10
#endif

// Default connection timeout in milliseconds
#ifndef W5500_DEFAULT_TIMEOUT
#define W5500_DEFAULT_TIMEOUT 5000
#endif

// Buffer sizes for W5500 sockets
#ifndef W5500_SOCKET_BUFFER_SIZE
#define W5500_SOCKET_BUFFER_SIZE 2048
#endif

// Enable large buffers if needed
#ifdef ETHERNET_LARGE_BUFFERS
#define W5500_LARGE_BUFFERS
#endif

// W5500 initialization function
// Call this before using any socket functions
bool W5500_init(uint8_t cs_pin);

// W5500 network configuration
bool W5500_config(uint8_t* mac, uint8_t* ip, uint8_t* gateway, uint8_t* subnet);

// W5500 DHCP configuration
bool W5500_configDHCP(uint8_t* mac);

#ifdef __cplusplus
}
#endif

#endif /* W5500_CONFIG_H_ */
