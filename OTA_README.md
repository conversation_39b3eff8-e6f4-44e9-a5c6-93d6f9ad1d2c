# ESP32 Ethernet OTA 更新系统

这个项目实现了基于以太网的ESP32固件无线更新(OTA)功能。

## 功能特性

- 🌐 基于以太网的HTTP服务器
- 📁 Web界面固件上传
- 📊 实时更新进度显示
- 🔒 安全的固件验证
- 📱 响应式Web界面
- 🔄 自动重启完成更新

## 硬件要求

- ESP32-S3开发板 (如 Seeed XIAO ESP32S3)
- W5500以太网模块
- 连接线缆

### 引脚连接

```
ESP32S3    W5500
------     -----
GPIO13  -> MISO
GPIO12  -> SCK  
GPIO11  -> MOSI
GPIO10  -> CS
GPIO9   -> RST
GPIO14  -> INT
3.3V    -> VCC
GND     -> GND
```

## 软件依赖

在 `platformio.ini` 中已配置以下库：
- `arduino-libraries/Ethernet @ ^2.0.2`
- `bblanchon/ArduinoJson @ ^7.0.4`

## 使用方法

### 1. 编译和上传初始固件

```bash
pio run -t upload
```

### 2. 连接网络

- 将以太网线连接到W5500模块
- 设备将自动获取DHCP IP地址
- 通过串口监视器查看分配的IP地址

### 3. 访问OTA界面

1. 打开浏览器访问: `http://[设备IP]:8080`
2. 您将看到OTA更新界面，包含：
   - 设备信息（IP地址、固件版本、剩余空间）
   - 文件上传表单
   - 使用说明

### 4. 执行OTA更新

1. 编译新的固件：`pio run`
2. 在Web界面选择 `.pio/build/seeed_xiao_esp32s3/firmware.bin` 文件
3. 点击"开始更新"按钮
4. 等待上传和更新完成
5. 设备将自动重启并运行新固件

## API接口

### GET /
返回OTA更新的Web界面

### POST /update
上传固件文件进行OTA更新
- Content-Type: multipart/form-data
- 文件字段名: firmware

### GET /status
返回JSON格式的设备状态：
```json
{
  "otaInProgress": false,
  "progress": 0,
  "freeSpace": 1234567,
  "version": "Dec 30 2024 10:30:00"
}
```

## 安全注意事项

1. **网络安全**: 此实现未包含身份验证，建议在受信任的网络环境中使用
2. **固件验证**: 系统会验证固件格式，但建议只上传可信的固件文件
3. **备份**: 在进行OTA更新前，建议备份当前工作的固件

## 故障排除

### 常见问题

1. **无法获取IP地址**
   - 检查以太网线连接
   - 确认路由器DHCP功能已启用
   - 检查W5500模块连接

2. **OTA更新失败**
   - 确认固件文件是正确的.bin格式
   - 检查文件大小不超过可用空间
   - 确保网络连接稳定

3. **Web界面无法访问**
   - 确认设备已获取IP地址
   - 检查防火墙设置
   - 尝试使用不同的浏览器

### 调试信息

通过串口监视器(115200波特率)可以查看详细的调试信息：
- 网络连接状态
- IP地址分配
- OTA更新进度
- 错误信息

## 扩展功能

可以考虑添加的功能：
- 用户身份验证
- HTTPS支持
- 固件版本管理
- 回滚功能
- 远程重启
- 配置管理界面

## 许可证

本项目基于MIT许可证开源。
