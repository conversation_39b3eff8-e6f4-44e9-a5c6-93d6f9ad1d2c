/*
 *  w5500_example.c
 *
 *  Copyright 2024
 *
 *  Example usage of W5500 socket implementation for lib60870
 */

 #include <Arduino.h>
 #include "Ethernet.h"
 #include "w5500_config.h"
 #include "hal_socket.h"


 
 // Example MAC address - change this for your device
 uint8_t mac[] = {0xDE, 0xAD, 0xBE, 0xEF, 0xFE, 0xED};
 
 // Example static IP configuration
 uint8_t ip[] = {192, 168, 1, 100};
 uint8_t gateway[] = {192, 168, 1, 1};
 uint8_t subnet[] = {255, 255, 255, 0};
 
 bool W5500_init(uint8_t cs_pin)
 {
     // Initialize Ethernet with CS pin
     Ethernet.init(cs_pin);
     
     // Try DHCP first
     if (Ethernet.begin(mac) == 0) {
         Serial0.println("Failed to configure Ethernet using DHCP");
         
         // Fall back to static IP
         Ethernet.begin(mac, IPAddress(ip), <PERSON><PERSON><PERSON>(gateway), <PERSON><PERSON><PERSON>(gateway), IPAddress(subnet));
         Serial0.print("Static IP configured: ");
     } else {
         Serial0.print("DHCP IP configured: ");
     }
     
     Serial0.println(Ethernet.localIP());
     
     // Check for Ethernet hardware present
     if (Ethernet.hardwareStatus() == EthernetNoHardware) {
         Serial0.println("Ethernet shield was not found.");
         return false;
     }
     
     if (Ethernet.linkStatus() == LinkOFF) {
         Serial0.println("Ethernet cable is not connected.");
         return false;
     }
     
     return true;
 }
 
 bool W5500_config(uint8_t* mac_addr, uint8_t* ip_addr, uint8_t* gateway_addr, uint8_t* subnet_addr)
 {
     Ethernet.begin(mac_addr, IPAddress(ip_addr), IPAddress(gateway_addr), IPAddress(gateway_addr), IPAddress(subnet_addr));
     
     Serial0.print("W5500 configured with IP: ");
     Serial0.println(Ethernet.localIP());
     
     return true;
 }
 
 bool W5500_configDHCP(uint8_t* mac_addr)
 {
     if (Ethernet.begin(mac_addr) == 0) {
         Serial0.println("Failed to configure Ethernet using DHCP");
         return false;
     }
     
     Serial0.print("W5500 DHCP configured with IP: ");
     Serial0.println(Ethernet.localIP());
     
     return true;
 }
 
 // Example usage for IEC 60870-5-104 server
 void example_iec104_server()
 {
     // Initialize W5500
     if (!W5500_init(W5500_CS_PIN)) {
         Serial0.println("Failed to initialize W5500");
         return;
     }
     
     // Create a TCP server socket for IEC 104 (port 2404)
     ServerSocket server = TcpServerSocket_create(NULL, 2404);
     if (server == NULL) {
         Serial0.println("Failed to create server socket");
         return;
     }
     
     // Start listening
     ServerSocket_listen(server);
     Serial0.println("IEC 104 server listening on port 2404");
     
     while (true) {
         // Accept incoming connections
         Socket client = ServerSocket_accept(server);
         if (client != NULL) {
             Serial0.println("Client connected");
             
             // Handle client communication here
             uint8_t buffer[256];
             int bytesRead = Socket_read(client, buffer, sizeof(buffer));
             if (bytesRead > 0) {
                 Serial0.print("Received ");
                 Serial0.print(bytesRead);
                 Serial0.println(" bytes");
                 
                 // Echo back the data
                 Socket_write(client, buffer, bytesRead);
             }
             
             // Clean up client socket
             Socket_destroy(client);
         }
         
         delay(10); // Small delay
     }
     
     // Clean up server socket
     ServerSocket_destroy(server);
 }
 
 // Example usage for IEC 60870-5-104 client
 void example_iec104_client()
 {
     // Initialize W5500
     if (!W5500_init(W5500_CS_PIN)) {
         Serial0.println("Failed to initialize W5500");
         return;
     }
     
     // Create a TCP client socket
     Socket client = TcpSocket_create();
     if (client == NULL) {
         Serial0.println("Failed to create client socket");
         return;
     }
     
     // Connect to IEC 104 server
     if (Socket_connect(client, "192.168.1.10", 2404)) {
         Serial0.println("Connected to IEC 104 server");
         
         // Send some data
         uint8_t data[] = {0x68, 0x04, 0x07, 0x00, 0x00, 0x00};
         int bytesSent = Socket_write(client, data, sizeof(data));
         if (bytesSent > 0) {
             Serial0.print("Sent ");
             Serial0.print(bytesSent);
             Serial0.println(" bytes");
         }
         
         // Read response
         uint8_t buffer[256];
         int bytesRead = Socket_read(client, buffer, sizeof(buffer));
         if (bytesRead > 0) {
             Serial0.print("Received ");
             Serial0.print(bytesRead);
             Serial0.println(" bytes");
         }
     } else {
         Serial0.println("Failed to connect to server");
     }
     
     // Clean up
     Socket_destroy(client);
 }
 
 // Example UDP usage
 void example_udp()
 {
     // Initialize W5500
     if (!W5500_init(W5500_CS_PIN)) {
         Serial0.println("Failed to initialize W5500");
         return;
     }
     
     // Create UDP socket
     UdpSocket udp = UdpSocket_create();
     if (udp == NULL) {
         Serial0.println("Failed to create UDP socket");
         return;
     }
     
     // Bind to port 8888
     if (UdpSocket_bind(udp, NULL, 8888)) {
         Serial0.println("UDP socket bound to port 8888");
         
         // Send a UDP packet
         uint8_t message[] = "Hello UDP!";
         if (UdpSocket_sendTo(udp, "192.168.1.255", 9999, message, sizeof(message))) {
             Serial0.println("UDP packet sent");
         }
         
         // Try to receive a packet
         uint8_t buffer[256];
         char sender[32];
         int bytesReceived = UdpSocket_receiveFrom(udp, sender, sizeof(sender), buffer, sizeof(buffer));
         if (bytesReceived > 0) {
             Serial0.print("Received UDP packet from ");
             Serial0.print(sender);
             Serial0.print(": ");
             Serial0.println(bytesReceived);
         }
     }
     
     // Note: UDP socket cleanup would be implemented when UdpSocket_destroy is added
 }
 